#!/usr/bin/env python3
"""
OPRO系统运行脚本 (OPRO System Runner)

用于运行集成了OPRO优化功能的多智能体交易系统。

使用示例:
    # 默认运行（OPRO优化模式）
    python run_opro_system.py --provider zhipuai

    # 运行基础评估（不含OPRO）
    python run_opro_system.py --provider zhipuai --mode evaluation --disable-opro

    # 运行OPRO优化循环（默认模式）
    python run_opro_system.py --provider zhipuai --mode optimization

    # 运行完整集成（评估+优化）
    python run_opro_system.py --provider zhipuai --mode integrated

    # 获取OPRO仪表板数据
    python run_opro_system.py --provider zhipuai --mode dashboard
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional, List

# 设置环境变量以支持UTF-8输出（Windows系统）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 尝试设置控制台代码页为UTF-8
    try:
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except Exception:
        pass

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.llm_interface import LLMInterface

# 导入数据存储模块
from data.integrated_data_manager import IntegratedDataManager

# 导入代理交互日志记录器
try:
    from data.agent_interaction_logger import AgentInteractionLogger
except ImportError:
    AgentInteractionLogger = None

def safe_log_message(message: str) -> str:
    """
    安全处理日志消息，替换可能导致编码错误的Unicode字符
    """
    if not isinstance(message, str):
        message = str(message)

    # 替换常见的Unicode字符
    replacements = {
        '✅': '[SUCCESS]',
        '❌': '[ERROR]',
        '🎉': '[CELEBRATION]',
        '⚠️': '[WARNING]',
        '📊': '[CHART]',
        '💡': '[IDEA]',
        '🔧': '[TOOL]',
        '📈': '[TRENDING_UP]',
        '📉': '[TRENDING_DOWN]'
    }

    for unicode_char, replacement in replacements.items():
        message = message.replace(unicode_char, replacement)

    return message

def setup_logging(verbose: bool = False, log_file: Optional[str] = None) -> logging.Logger:
    """设置日志记录"""
    log_level = logging.DEBUG if verbose else logging.INFO

    # 创建自定义格式化器，处理Unicode字符
    class SafeFormatter(logging.Formatter):
        def format(self, record):
            try:
                # 尝试正常格式化
                formatted = super().format(record)
                return formatted
            except UnicodeEncodeError:
                # 如果遇到编码错误，替换特殊字符
                if hasattr(record, 'msg'):
                    # 替换常见的Unicode字符
                    msg = str(record.msg)
                    msg = msg.replace('✅', '[SUCCESS]')
                    msg = msg.replace('❌', '[ERROR]')
                    msg = msg.replace('🎉', '[CELEBRATION]')
                    record.msg = msg

                if record.args:
                    safe_args = []
                    for arg in record.args:
                        if isinstance(arg, str):
                            safe_arg = arg.replace('✅', '[SUCCESS]')
                            safe_arg = safe_arg.replace('❌', '[ERROR]')
                            safe_arg = safe_arg.replace('🎉', '[CELEBRATION]')
                            safe_args.append(safe_arg)
                        else:
                            safe_args.append(arg)
                    record.args = tuple(safe_args)

                return super().format(record)

    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

    handlers: List[logging.Handler] = [console_handler]

    if log_file:
        # 文件处理器使用UTF-8编码
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        handlers.append(file_handler)

    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        force=True  # 强制重新配置
    )

    return logging.getLogger(__name__)

def load_config(config_path: str = "config/opro_config.json") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print(f"配置文件不存在: {config_path}")
            return {}
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {}

def create_system_config(args) -> Dict[str, Any]:
    """创建系统配置"""
    return {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "stocks": ["AAPL"],  # 可以通过参数扩展
        "starting_cash": 1000000,
        "simulation_days": args.simulation_days,
        "verbose": args.verbose,
        "enable_concurrent_execution": not args.disable_concurrent,
        "fail_on_large_gaps": False,
        "fill_date_gaps": True
    }

def run_evaluation_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行评估模式"""
    logger.info("=" * 80)
    logger.info("运行模式: 标准评估")
    logger.info("=" * 80)
    
    try:
        if args.quick_test:
            logger.info("运行快速测试...")
            result = assessor.run_quick_test()
        else:
            logger.info("运行完整评估...")
            result = assessor.run(
                target_agents=args.agents.split(',') if args.agents else None,
                max_coalitions=args.max_coalitions
            )
        
        return {
            "mode": "evaluation", 
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"评估模式执行失败: {e}")
        return {
            "mode": "evaluation",
            "success": False,
            "error": str(e)
        }

def run_optimization_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行优化模式"""
    logger.info("=" * 80)
    logger.info("运行模式: OPRO优化")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行优化模式")
        return {
            "mode": "optimization",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        target_agents = args.agents.split(',') if args.agents else None
        
        logger.info(f"开始OPRO优化循环...")
        result = assessor.run_opro_optimization_cycle(
            target_agents=target_agents,
            force_optimization=args.force_optimization
        )
        
        return {
            "mode": "optimization",
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"优化模式执行失败: {e}")
        return {
            "mode": "optimization",
            "success": False,
            "error": str(e)
        }

def run_integrated_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行集成模式（评估+优化）"""
    logger.info("=" * 80)
    logger.info("运行模式: 集成模式（评估+优化）")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行集成模式")
        return {
            "mode": "integrated",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        target_agents = args.agents.split(',') if args.agents else None
        
        result = assessor.run_with_opro_integration(
            target_agents=target_agents,
            max_coalitions=args.max_coalitions,
            run_optimization_before=args.optimize_before,
            run_optimization_after=args.optimize_after
        )
        
        return {
            "mode": "integrated",
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"集成模式执行失败: {e}")
        return {
            "mode": "integrated",
            "success": False,
            "error": str(e)
        }

def run_dashboard_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行仪表板模式"""
    logger.info("=" * 80)
    logger.info("运行模式: OPRO仪表板")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行仪表板模式")
        return {
            "mode": "dashboard",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        dashboard_data = assessor.get_opro_dashboard_data()
        
        # 输出仪表板数据
        logger.info("OPRO系统状态:")
        
        # 系统统计
        system_stats = dashboard_data.get("system_stats", {})
        if "optimizer" in system_stats:
            opt_stats = system_stats["optimizer"]
            logger.info(f"  优化器统计:")
            logger.info(f"    总优化次数: {opt_stats.get('total_optimizations', 0)}")
            logger.info(f"    成功优化次数: {opt_stats.get('successful_optimizations', 0)}")
            logger.info(f"    成功率: {opt_stats.get('success_rate', 0):.1f}%")
        
        # 智能体性能
        agent_performance = dashboard_data.get("agent_performance", {})
        if "ranking" in agent_performance:
            logger.info(f"  智能体性能排名:")
            for i, agent_id in enumerate(agent_performance["ranking"][:5]):
                stats = agent_performance["agent_stats"].get(agent_id, {})
                avg_score = stats.get("average_score", 0)
                logger.info(f"    {i+1}. {agent_id}: {avg_score:.6f}")
        
        # 优化建议
        recommendations = dashboard_data.get("recommendations", [])
        if recommendations:
            logger.info(f"  优化建议:")
            for rec in recommendations[:3]:
                priority = rec.get("priority", "medium")
                message = rec.get("message", "")
                logger.info(f"    [{priority.upper()}] {message}")
        
        return {
            "mode": "dashboard",
            "result": dashboard_data,
            "success": dashboard_data.get("opro_enabled", False)
        }
        
    except Exception as e:
        logger.error(f"仪表板模式执行失败: {e}")
        return {
            "mode": "dashboard",
            "success": False,
            "error": str(e)
        }

def export_results(result: Dict[str, Any], output_path: str, logger: logging.Logger):
    """导出结果到文件"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已导出至: {output_path}")
        
    except Exception as e:
        logger.error(f"导出结果失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPRO系统运行脚本")
    
    # 基础参数
    parser.add_argument("--provider", type=str, default="zhipuai", 
                       choices=["zhipuai", "openai"], help="LLM提供商")
    parser.add_argument("--mode", type=str, default="integrated",
                       choices=["evaluation", "optimization", "integrated", "dashboard"],
                       help="运行模式")
    parser.add_argument("--enable-opro", action="store_true", default=True, help="启用OPRO功能")
    parser.add_argument("--disable-opro", action="store_true", help="禁用OPRO功能")
    parser.add_argument("--verbose", action="store_true", help="详细日志")
    
    # 系统配置
    parser.add_argument("--start-date", type=str, default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", type=str, default="2025-04-01", help="结束日期")
    parser.add_argument("--simulation-days", type=int, help="模拟天数")
    parser.add_argument("--agents", type=str, help="目标智能体列表（逗号分隔）")
    parser.add_argument("--max-coalitions", type=int, help="最大联盟数量")
    parser.add_argument("--disable-concurrent", action="store_true", help="禁用并发执行")
    
    # 测试选项
    parser.add_argument("--quick-test", action="store_true", help="运行快速测试")
    
    # 优化选项
    parser.add_argument("--force-optimization", action="store_true", help="强制优化")
    parser.add_argument("--optimize-before", action="store_true", help="评估前优化")
    parser.add_argument("--optimize-after", action="store_true", default=True, help="评估后优化")
    
    # 输出选项
    parser.add_argument("--output", type=str, help="结果输出文件")
    parser.add_argument("--log-file", type=str, help="日志文件")
    parser.add_argument("--export-opro-data", action="store_true", help="导出OPRO数据")

    # 代理日志记录选项
    parser.add_argument("--enable-agent-logging", action="store_true", default=True,
                       help="启用代理交互日志记录")
    parser.add_argument("--disable-agent-logging", action="store_true",
                       help="禁用代理交互日志记录")
    parser.add_argument("--agent-log-path", type=str, default="data/trading",
                       help="代理日志存储路径")
    parser.add_argument("--agent-log-date", type=str,
                       help="代理日志实验日期 (YYYY-MM-DD格式，默认为当前日期)")
    parser.add_argument("--export-agent-logs", action="store_true",
                       help="导出代理交互日志")
    parser.add_argument("--agent-log-summary", action="store_true",
                       help="显示代理日志摘要")

    # 数据存储选项
    parser.add_argument("--enable-data-storage", action="store_true", default=True, help="启用综合数据存储")
    parser.add_argument("--disable-data-storage", action="store_true", help="禁用综合数据存储")
    parser.add_argument("--export-all-data", action="store_true", help="导出所有数据")
    parser.add_argument("--create-backup", action="store_true", help="创建数据备份")
    parser.add_argument("--data-report", action="store_true", help="生成数据报告")
    parser.add_argument("--analysis-report", type=str, choices=["trading", "optimization", "comprehensive"],
                       help="生成分析报告")
    parser.add_argument("--export-format", type=str, default="excel", choices=["excel", "csv", "json"],
                       help="数据导出格式")
    parser.add_argument("--backup-type", type=str, default="auto", choices=["full", "incremental", "auto"],
                       help="备份类型")
    parser.add_argument("--backup-status", action="store_true", help="显示备份状态")
    parser.add_argument("--list-backups", action="store_true", help="列出所有备份")
    
    # 配置文件
    parser.add_argument("--config", type=str, default="config/opro_config.json", 
                       help="配置文件路径")
    
    args = parser.parse_args()


    
    # 处理OPRO开关逻辑
    if args.disable_opro:
        args.enable_opro = False

    # 处理数据存储开关逻辑
    if args.disable_data_storage:
        args.enable_data_storage = False

    # 处理代理日志记录开关逻辑
    if args.disable_agent_logging:
        args.enable_agent_logging = False
    
    # 设置日志
    log_file = args.log_file or f"opro_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logger = setup_logging(args.verbose, log_file)
    
    logger.info("=" * 100)
    logger.info("OPRO系统启动")
    logger.info("=" * 100)
    logger.info(f"运行模式: {args.mode}")
    logger.info(f"LLM提供商: {args.provider}")
    logger.info(f"OPRO启用: {args.enable_opro}")
    logger.info(f"数据存储启用: {args.enable_data_storage}")
    logger.info(f"代理日志记录启用: {args.enable_agent_logging}")
    
    try:
        # 加载配置
        config_data = load_config(args.config)
        
        # 创建系统配置
        system_config = create_system_config(args)



        # 初始化系统
        logger.info("初始化系统...")
        
        # 创建LLM接口
        llm_interface = LLMInterface(provider=args.provider, logger=logger)

        # 初始化代理交互日志记录器
        interaction_logger = None
        if args.enable_agent_logging and AgentInteractionLogger:
            try:
                # 设置实验日期
                experiment_date = args.agent_log_date or datetime.now().strftime("%Y-%m-%d")

                interaction_logger = AgentInteractionLogger(
                    base_path=args.agent_log_path,
                    enabled=True,
                    logger=logger
                )

                # 设置实验日期
                interaction_logger.set_experiment_date(experiment_date)

                logger.info(f"代理交互日志记录器初始化成功 (实验日期: {experiment_date})")
            except Exception as e:
                logger.warning(f"代理交互日志记录器初始化失败: {e}")
                interaction_logger = None
        else:
            logger.info("代理交互日志记录功能已禁用")

        # 初始化集成数据管理器
        data_manager = None
        if args.enable_data_storage:
            try:
                data_manager = IntegratedDataManager(config=config_data, logger=logger)
                logger.info("集成数据管理器初始化成功")
            except Exception as e:
                logger.warning(f"集成数据管理器初始化失败: {e}")
        else:
            logger.info("数据存储功能已禁用")

        # 创建评估器
        opro_config = {}
        if config_data:
            # 合并所有相关的配置部分
            opro_config.update(config_data.get("optimization", {}))
            opro_config.update(config_data.get("evaluation", {}))
            opro_config.update(config_data.get("storage", {}))

        assessor = ContributionAssessor(
            config=system_config,
            logger=logger,
            llm_provider=args.provider,
            enable_opro=args.enable_opro,
            opro_config=opro_config,
            interaction_logger=interaction_logger
        )
        
        logger.info("系统初始化完成")
        
        # 根据模式运行
        if args.mode == "evaluation":
            result = run_evaluation_mode(assessor, args, logger)
        elif args.mode == "optimization":
            result = run_optimization_mode(assessor, args, logger)
        elif args.mode == "integrated":
            result = run_integrated_mode(assessor, args, logger)
        elif args.mode == "dashboard":
            result = run_dashboard_mode(assessor, args, logger)
        else:
            raise ValueError(f"未知运行模式: {args.mode}")

        # 处理数据存储（如果启用）
        if data_manager and data_manager.enabled and result.get("success", False):
            try:
                # 处理评估结果
                if "result" in result and isinstance(result["result"], dict):
                    processing_summary = data_manager.process_assessment_result(result["result"])
                    result["data_processing"] = processing_summary
                    logger.info("评估结果数据处理完成")

                # 如果是优化模式，跟踪提示词优化
                if args.mode in ["optimization", "integrated"] and "result" in result:
                    optimization_result = result["result"].get("optimization_result", {})
                    if optimization_result.get("success", False):
                        # 这里可以添加提示词优化跟踪逻辑
                        logger.info("提示词优化数据跟踪完成")

            except Exception as e:
                logger.error(f"数据处理失败: {e}")
                result["data_processing_error"] = str(e)
        
        # 添加执行信息
        result.update({
            "execution_info": {
                "timestamp": datetime.now().isoformat(),
                "mode": args.mode,
                "opro_enabled": args.enable_opro,
                "llm_provider": args.provider,
                "config_file": args.config
            }
        })
        
        # 输出结果
        success = result.get("success", False)
        logger.info("=" * 100)
        if success:
            logger.info(safe_log_message("🎉 执行成功!"))
        else:
            logger.error(safe_log_message("❌ 执行失败!"))
            if "error" in result:
                logger.error(f"错误: {result['error']}")
        logger.info("=" * 100)
        
        # 导出结果
        if args.output:
            export_results(result, args.output, logger)
        
        # 导出OPRO数据
        if args.export_opro_data and assessor.enable_opro:
            export_result = assessor.export_opro_data()
            if export_result.get("success", False):
                logger.info(f"OPRO数据已导出至: {export_result['export_directory']}")

        # 处理代理日志记录相关命令
        if interaction_logger and interaction_logger.enabled:
            # 显示代理日志摘要
            if args.agent_log_summary:
                summary = interaction_logger.get_all_agents_summary()
                if summary.get("enabled", False):
                    logger.info("=" * 60)
                    logger.info("代理交互日志摘要")
                    logger.info("=" * 60)
                    logger.info(f"实验日期: {summary['experiment_date']}")
                    logger.info(f"日志目录: {summary['base_directory']}")
                    logger.info(f"代理总数: {summary['total_agents']}")

                    for agent_name, agent_summary in summary.get("agents", {}).items():
                        logger.info(f"  {agent_name}:")
                        logger.info(f"    输入记录: {agent_summary.get('inputs_count', 0)}")
                        logger.info(f"    提示词记录: {agent_summary.get('prompts_count', 0)}")
                        logger.info(f"    输出记录: {agent_summary.get('outputs_count', 0)}")

                    logger.info("=" * 60)
                else:
                    logger.error("获取代理日志摘要失败")

            # 导出代理日志
            if args.export_agent_logs:
                # 获取所有代理的日志摘要
                summary = interaction_logger.get_all_agents_summary()
                if summary.get("enabled", False) and summary.get("agents"):
                    logger.info("导出代理交互日志...")

                    for agent_name in summary["agents"].keys():
                        export_result = interaction_logger.export_agent_logs(
                            agent_name=agent_name,
                            export_format="json"
                        )

                        if export_result.get("success", False):
                            logger.info(f"代理 {agent_name} 日志已导出至: {export_result['export_path']}")
                        else:
                            logger.error(f"导出代理 {agent_name} 日志失败: {export_result.get('error', '未知错误')}")
                else:
                    logger.warning("没有找到代理日志数据可供导出")

        # 处理数据存储相关命令
        if data_manager and data_manager.enabled:
            # 导出所有数据
            if args.export_all_data:
                export_result = data_manager.export_all_data(export_format=args.export_format)
                if export_result.get("success", False):
                    logger.info(f"所有数据已导出至: {export_result['export_directory']}")
                else:
                    logger.error(f"导出数据失败: {export_result.get('error', '未知错误')}")

            # 生成分析报告
            if args.analysis_report:
                analysis_result = data_manager.generate_analysis_report(report_type=args.analysis_report)
                if analysis_result.get("success", False):
                    logger.info(f"分析报告已生成: {analysis_result.get('export_directory', analysis_result.get('dashboard_directory', ''))}")

                    # 显示报告摘要
                    summary = analysis_result.get("analysis_summary", {})
                    if summary:
                        logger.info("分析报告摘要:")
                        for key, value in summary.items():
                            logger.info(f"  {key}: {value}")
                else:
                    logger.error(f"生成分析报告失败: {analysis_result.get('error', '未知错误')}")

            # 创建备份
            if args.create_backup:
                backup_result = data_manager.create_backup(backup_type=args.backup_type)
                if backup_result.get("success", False):
                    logger.info(f"数据备份已创建: {backup_result['backup_path']} ({backup_result['backup_size_mb']} MB)")
                else:
                    logger.error(f"创建备份失败: {backup_result.get('error', '未知错误')}")

            # 显示备份状态
            if args.backup_status:
                backup_status = data_manager.get_backup_status()
                if backup_status.get("enabled", False):
                    logger.info("=" * 60)
                    logger.info("备份系统状态")
                    logger.info("=" * 60)
                    logger.info(f"自动备份运行: {backup_status.get('auto_backup_running', False)}")
                    logger.info(f"总备份数: {backup_status.get('total_backups', 0)}")
                    logger.info(f"完整备份数: {backup_status.get('full_backups', 0)}")
                    logger.info(f"增量备份数: {backup_status.get('incremental_backups', 0)}")
                    logger.info(f"总大小: {backup_status.get('total_size_mb', 0):.2f} MB")

                    latest_backup = backup_status.get('latest_backup')
                    if latest_backup:
                        logger.info(f"最新备份: {latest_backup['backup_id']} ({latest_backup['backup_type']})")
                        logger.info(f"最新备份时间: {latest_backup['timestamp']}")

                    logger.info("=" * 60)
                else:
                    logger.error("备份系统未启用或出现错误")

            # 列出备份
            if args.list_backups:
                backups = data_manager.list_backups()
                if backups:
                    logger.info("=" * 80)
                    logger.info("备份列表")
                    logger.info("=" * 80)
                    logger.info(f"{'备份ID':<30} {'类型':<12} {'时间':<20} {'大小(MB)':<10} {'状态':<8}")
                    logger.info("-" * 80)

                    for backup in backups[:10]:  # 显示最近10个备份
                        status = "存在" if backup.get("exists", False) else "缺失"
                        logger.info(f"{backup['backup_id']:<30} {backup['backup_type']:<12} "
                                  f"{backup['timestamp'][:19]:<20} {backup['file_size_mb']:<10.2f} {status:<8}")

                    if len(backups) > 10:
                        logger.info(f"... 还有 {len(backups) - 10} 个备份")

                    logger.info("=" * 80)
                else:
                    logger.info("暂无备份记录")

            # 生成数据报告
            if args.data_report:
                report = data_manager.generate_comprehensive_report()
                if report.get("enabled", False):
                    logger.info("=" * 60)
                    logger.info("数据存储系统报告")
                    logger.info("=" * 60)

                    # 存储统计
                    storage_stats = report.get("storage_statistics", {})
                    if storage_stats:
                        logger.info(f"数据库大小: {storage_stats.get('database_size_mb', 0)} MB")
                        logger.info(f"交易会话数: {storage_stats.get('total_trading_sessions', 0)}")
                        logger.info(f"提示词优化数: {storage_stats.get('total_prompt_optimizations', 0)}")

                    # 建议
                    recommendations = report.get("recommendations", [])
                    if recommendations:
                        logger.info("建议:")
                        for rec in recommendations:
                            logger.info(f"  - {rec}")

                    logger.info("=" * 60)
                else:
                    logger.error("生成数据报告失败")

        # 返回适当的退出代码
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"系统执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()