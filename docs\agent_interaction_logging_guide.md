# 代理交互日志记录功能使用指南

## 概述

代理交互日志记录功能为OPRO系统提供了全面的代理行为跟踪能力，能够详细记录每个代理的输入、提示词和输出数据，便于分析、调试和优化代理性能。

## 功能特性

### 🎯 核心功能
- **输入记录**: 记录代理接收到的所有输入数据（市场数据、状态信息、前序输出等）
- **提示词记录**: 记录代理使用的提示词模板和完整提示词
- **输出记录**: 记录代理的推理输出、决策结果和置信度
- **时间戳跟踪**: 为所有记录添加精确的时间戳
- **关联追踪**: 通过ID关联输入、提示词和输出记录

### 📁 文件结构
```
/data/trading/{experiment_date}/{agent_name}/
├── inputs.json     - 代理输入数据
├── prompts.json    - 代理提示词数据
└── outputs.json    - 代理输出数据
```

### 🔧 配置选项
- **可启用/禁用**: 通过命令行参数控制
- **自定义路径**: 可指定日志存储路径
- **实验日期**: 支持自定义实验日期
- **并发安全**: 支持多线程环境下的安全记录

## 命令行使用

### 基本用法

#### 启用代理日志记录（默认启用）
```bash
python run_opro_system.py --provider zhipuai --enable-agent-logging
```

#### 禁用代理日志记录
```bash
python run_opro_system.py --provider zhipuai --disable-agent-logging
```

#### 自定义日志路径和日期
```bash
python run_opro_system.py --provider zhipuai \
    --agent-log-path "custom_logs/trading" \
    --agent-log-date "2025-07-04"
```

### 高级功能

#### 显示代理日志摘要
```bash
python run_opro_system.py --provider zhipuai --agent-log-summary
```

#### 导出代理日志
```bash
python run_opro_system.py --provider zhipuai --export-agent-logs
```

#### 完整示例
```bash
# 运行系统并启用所有日志功能
python run_opro_system.py \
    --provider zhipuai \
    --mode integrated \
    --enable-agent-logging \
    --agent-log-date "2025-07-04" \
    --agent-log-summary \
    --export-agent-logs
```

## 编程接口使用

### 基本使用

```python
from data.agent_interaction_logger import AgentInteractionLogger

# 创建日志记录器
logger = AgentInteractionLogger(
    base_path="data/trading",
    enabled=True
)

# 设置实验日期
logger.set_experiment_date("2025-07-04")

# 记录代理输入
input_id = logger.log_agent_input(
    agent_name="NAA",
    state_data={"current_date": "2025-07-04"},
    market_data={"AAPL": {"price": 150.0}},
    previous_outputs={},
    metadata={"session": "test"}
)

# 记录代理提示词
prompt_id = logger.log_agent_prompt(
    agent_name="NAA",
    prompt_template="你是一个新闻分析师...",
    full_prompt="完整的提示词内容...",
    prompt_version="1.0.0",
    source="default"
)

# 记录代理输出
output_id = logger.log_agent_output(
    agent_name="NAA",
    input_id=input_id,
    prompt_id=prompt_id,
    raw_response="原始LLM响应",
    parsed_output={"sentiment": 0.5},
    processing_time=1.2,
    confidence=0.8,
    reasoning="分析推理过程"
)
```

### 与代理集成

```python
from data.agent_interaction_logger import AgentInteractionLogger
from agents.analyst_agents import NewsAnalystAgent

# 创建日志记录器
interaction_logger = AgentInteractionLogger(
    base_path="data/trading",
    enabled=True
)

# 创建代理（自动集成日志记录）
agent = NewsAnalystAgent(
    llm_interface=llm_interface,
    logger=logger,
    interaction_logger=interaction_logger
)

# 代理处理会自动记录日志
result = agent.process(state_data)
```

## 数据格式说明

### 输入数据格式 (inputs.json)
```json
[
  {
    "timestamp": "2025-07-04T10:30:00.123456",
    "input_id": "input_20250704_103000_abc12345",
    "state_data": {
      "current_date": "2025-07-04",
      "stock_data": {...}
    },
    "market_data": {
      "AAPL": {"price": 150.0, "volume": 1000000}
    },
    "previous_outputs": {...},
    "metadata": {"analysis_count": 1}
  }
]
```

### 提示词数据格式 (prompts.json)
```json
[
  {
    "timestamp": "2025-07-04T10:30:01.123456",
    "prompt_id": "prompt_20250704_103001_def67890",
    "prompt_template": "你是一个专业的新闻分析师...",
    "full_prompt": "完整的提示词内容...",
    "prompt_version": "1.0.0",
    "source": "default",
    "metadata": {"state_info_length": 500}
  }
]
```

### 输出数据格式 (outputs.json)
```json
[
  {
    "timestamp": "2025-07-04T10:30:02.123456",
    "output_id": "output_20250704_103002_ghi34567",
    "input_id": "input_20250704_103000_abc12345",
    "prompt_id": "prompt_20250704_103001_def67890",
    "raw_response": "原始LLM响应",
    "parsed_output": {
      "sentiment": 0.5,
      "confidence": 0.8,
      "analysis": "市场情绪分析结果"
    },
    "processing_time": 1.234,
    "llm_used": true,
    "confidence": 0.8,
    "reasoning": "基于新闻数据的分析推理",
    "metadata": {
      "analysis_count": 1,
      "total_processing_time": 1.234
    }
  }
]
```

## 管理和维护

### 查看日志摘要
```python
# 获取单个代理的日志摘要
summary = logger.get_agent_log_summary("NAA")
print(f"输入记录: {summary['inputs_count']}")
print(f"提示词记录: {summary['prompts_count']}")
print(f"输出记录: {summary['outputs_count']}")

# 获取所有代理的日志摘要
all_summary = logger.get_all_agents_summary()
for agent_name, agent_summary in all_summary["agents"].items():
    print(f"{agent_name}: {agent_summary['outputs_count']} 个输出")
```

### 导出日志数据
```python
# 导出单个代理的日志
export_result = logger.export_agent_logs("NAA", "json")
if export_result["success"]:
    print(f"导出成功: {export_result['export_path']}")

# 导出为CSV格式
export_result = logger.export_agent_logs("NAA", "csv")
```

### 清理旧日志
```python
# 清理30天前的日志
logger.cleanup_old_logs(days_to_keep=30)
```

## 性能考虑

### 优化建议
1. **批量写入**: 系统使用内存缓存减少磁盘I/O
2. **异步处理**: 日志记录不会阻塞代理处理
3. **文件大小控制**: 自动分割大文件
4. **定期清理**: 定期清理旧日志文件

### 存储空间估算
- 每个代理每天约产生 1-10MB 日志数据
- 7个代理运行30天约需要 200MB-3GB 存储空间
- 建议定期备份和清理旧数据

## 故障排除

### 常见问题

#### 1. 日志文件未创建
**原因**: 日志记录功能被禁用或路径权限不足
**解决**: 检查 `--enable-agent-logging` 参数和目录权限

#### 2. 记录数据不完整
**原因**: 代理创建时未传递 `interaction_logger`
**解决**: 确保使用更新后的代理工厂创建代理

#### 3. 性能影响
**原因**: 频繁的磁盘写入
**解决**: 使用SSD存储或调整缓存策略

### 调试模式
```python
# 启用详细日志
logger = AgentInteractionLogger(
    base_path="data/trading",
    enabled=True,
    logger=logging.getLogger("debug")
)
logger.logger.setLevel(logging.DEBUG)
```

## 最佳实践

1. **命名规范**: 使用有意义的实验日期和路径
2. **定期备份**: 重要实验数据应及时备份
3. **监控存储**: 定期检查磁盘空间使用情况
4. **数据分析**: 利用导出功能进行离线分析
5. **版本控制**: 记录提示词版本变化

## 示例脚本

完整的测试和使用示例请参考：
- `test_agent_logging.py` - 功能测试脚本
- `examples/agent_logging_demo.py` - 使用示例

## 更新日志

- **v1.0.0** (2025-07-04): 初始版本发布
  - 基础日志记录功能
  - 命令行接口
  - 文件导出功能
  - 与现有代理系统集成
